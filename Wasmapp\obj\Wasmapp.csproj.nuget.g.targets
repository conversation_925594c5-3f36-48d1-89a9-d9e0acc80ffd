﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.net.sdk.webassembly.pack\9.0.5\build\Microsoft.NET.Sdk.WebAssembly.Pack.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.net.sdk.webassembly.pack\9.0.5\build\Microsoft.NET.Sdk.WebAssembly.Pack.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.5\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.5\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.devserver\9.0.5\build\Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.devserver\9.0.5\build\Microsoft.AspNetCore.Components.WebAssembly.DevServer.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.analyzers\9.0.5\buildTransitive\netstandard2.0\Microsoft.AspNetCore.Components.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.analyzers\9.0.5\buildTransitive\netstandard2.0\Microsoft.AspNetCore.Components.Analyzers.targets')" />
  </ImportGroup>
</Project>