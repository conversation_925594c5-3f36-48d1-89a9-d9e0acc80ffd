{"GlobalPropertiesHash": "4Pwv/SVdsVxA6SiIki6jK4tGr3BZsrVY8Xt1CvW9BAU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["ZnXh7MvkSEB2YYEumnHcMiKJ5NmE3pqTT+k91orM2AM=", "P5esjfYQ0Kx2sHBjVCiwoN6E3YFJsEf+8T804QDXjkY=", "8IjlGoXdE+icmWZtZJxDn9HIiCxZlc9mVS6DM9e3m1o=", "/+CmNh0KRsvvv1L9+3f+qhuguiVeZ8lYJTkznJjUdho=", "RZlbbW+SHd79jLW2TbTxwN5In6i3czO0mlQIqHBddvY=", "Iz+D8V2LayTzTYE5mHxaGfC5SiYrAe51ststlOcg84w=", "+Fb31aO9k9Yx9TbPFl6NQ8zeuc7qUSIAjAd91hJX9vk=", "umpyqxq5jE8We8S1d9eqtW3UzfnMCHsfdOm+j0ty4Lg=", "fwucgGD6ZbI00xok6rISRAzJSQMSb6GOO4gi4fNjVeg="], "CachedAssets": {"ZnXh7MvkSEB2YYEumnHcMiKJ5NmE3pqTT+k91orM2AM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\css\\app.css", "SourceId": "Wasmapp", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81sotqnb8s", "Integrity": "FEcjGL5Vayj/vVFfza9Fl7AOyv/PLNTZoBBgB6QciYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 3677, "LastWriteTime": "2025-07-12T10:10:26.50383+00:00"}, "P5esjfYQ0Kx2sHBjVCiwoN6E3YFJsEf+8T804QDXjkY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\icon-192.png", "SourceId": "Wasmapp", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-07-12T10:10:26.5408293+00:00"}, "8IjlGoXdE+icmWZtZJxDn9HIiCxZlc9mVS6DM9e3m1o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\icon-512.png", "SourceId": "Wasmapp", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-512#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kjimucelzx", "Integrity": "oEo6d+KqX5fjxTiZk/w9NB3Mi0+ycS5yLwCKwr4IkbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-512.png", "FileLength": 6311, "LastWriteTime": "2025-07-12T10:10:26.5424333+00:00"}, "/+CmNh0KRsvvv1L9+3f+qhuguiVeZ8lYJTkznJjUdho=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\index.html", "SourceId": "Wasmapp", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tmuoxmgekt", "Integrity": "FQraHv35Ol9kH8JgiSSQcZpDRvKO6FBrEehpH0vA/f4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 1193, "LastWriteTime": "2025-07-12T10:10:26.5457326+00:00"}, "RZlbbW+SHd79jLW2TbTxwN5In6i3czO0mlQIqHBddvY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\manifest.webmanifest", "SourceId": "Wasmapp", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\Wasmapp\\Wasmapp\\wwwroot\\", "BasePath": "/", "RelativePath": "manifest#[.{fingerprint}]?.webmanifest", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p3vx2mqehq", "Integrity": "tZVd1FGiVrMQGVwk5tbCvy00aS6Xjjm5kZNpbQoZSkY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\manifest.webmanifest", "FileLength": 441, "LastWriteTime": "2025-07-12T10:10:26.5467444+00:00"}}, "CachedCopyCandidates": {}}